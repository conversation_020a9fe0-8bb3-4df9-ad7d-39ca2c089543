from astrbot.api.event import AstrMessageEvent
from astrbot.api.star import Context, Star, register
from astrbot.api import logger
from astrbot.api.all import *

# 尝试导入command装饰器
try:
    from astrbot.api.star import command
except ImportError:
    try:
        from astrbot.api.all import command
    except ImportError:
        # 如果无法导入，定义一个简单的替代
        def command(cmd_name):
            def decorator(func):
                func._command_name = cmd_name
                return func
            return decorator
from typing import Dict, List, Optional
from collections import defaultdict, deque
import asyncio
import concurrent.futures


@register(
    "astrbot_plugin_dzmm",
    "Assistant",
    "Astrbot AI聊天插件，支持上下文对话和自定义配置",
    "1.0.0",
    "https://github.com/user/astrbot_plugin_dzmm",
)
class PluginDzmm(Star):
    def __init__(self, context: Context, config: dict):
        super().__init__(context)
        self.config = config

        # 配置参数
        self.api_key = self.config.get("api_key", "")
        self.system_prompt = self.config.get("system_prompt", "你是一个有帮助的AI助手。")
        self.context_length = self.config.get("context_length", 10)
        self.api_url = self.config.get("api_url", "https://www.gpt4novel.com/api/xiaoshuoai/ext/v1/chat/completions")
        self.model = self.config.get("model", "nalang-turbo-v23")
        self.temperature = self.config.get("temperature", 0.7)
        self.max_tokens = self.config.get("max_tokens", 800)
        self.top_p = self.config.get("top_p", 0.35)
        self.repetition_penalty = self.config.get("repetition_penalty", 1.05)

        # 新增：角色卡配置
        self.personas = self.config.get("角色卡", {
            "默认助手": "你是一个有帮助的AI助手。",
            "编程助手": "你是一个专业的编程助手，擅长解答编程问题和提供代码建议。",
            "创意写作": "你是一个富有创意的写作助手，能够帮助用户进行创意写作和文案创作。"
        })

        # 新增：API密钥配置
        self.api_keys = self.config.get("api_keys", {
            "主要密钥": self.api_key or "your-api-key-here"
        })

        # 新增：当前选中的角色卡和API密钥（每个用户独立）
        self.user_current_persona: Dict[str, str] = defaultdict(lambda: self._get_first_persona_name())
        self.user_current_api_key: Dict[str, str] = defaultdict(lambda: self._get_first_api_key_name())

        # 用户上下文存储 - 使用用户ID+群组ID作为键
        self.user_contexts: Dict[str, deque] = defaultdict(lambda: deque(maxlen=self.context_length))

        # 验证API密钥
        if not self.api_key and not any(self.api_keys):
            logger.warning("DZMM插件: 未配置API密钥，插件将无法正常工作")

    def _get_first_persona_name(self) -> str:
        """获取第一个角色卡的名称"""
        if self.personas and isinstance(self.personas, dict):
            return list(self.personas.keys())[0]
        return "默认助手"

    def _get_first_api_key_name(self) -> str:
        """获取第一个API密钥的名称"""
        if self.api_keys and isinstance(self.api_keys, dict):
            return list(self.api_keys.keys())[0]
        return "主要密钥"

    def get_persona_by_name(self, name: str) -> Optional[str]:
        """根据名称获取角色卡内容"""
        if isinstance(self.personas, dict):
            return self.personas.get(name)
        return None

    def get_api_key_by_name(self, name: str) -> Optional[str]:
        """根据名称获取API密钥"""
        if isinstance(self.api_keys, dict):
            return self.api_keys.get(name)
        return None

    def get_persona_names(self) -> List[str]:
        """获取所有角色卡名称"""
        if isinstance(self.personas, dict):
            return list(self.personas.keys())
        return []

    def get_api_key_names(self) -> List[str]:
        """获取所有API密钥名称"""
        if isinstance(self.api_keys, dict):
            return list(self.api_keys.keys())
        return []

    def get_user_key(self, event: AstrMessageEvent) -> str:
        """生成用户唯一标识"""
        user_id = event.get_sender_id() or "unknown"
        group_id = event.get_group_id() or "private"
        platform = event.get_platform_name() or "unknown"
        return f"{platform}_{group_id}_{user_id}"

    def add_to_context(self, user_key: str, role: str, content: str):
        """添加消息到用户上下文"""
        self.user_contexts[user_key].append({"role": role, "content": content})

    def get_context_messages(self, user_key: str) -> List[dict]:
        """获取用户的上下文消息"""
        # 获取当前用户选中的角色卡
        current_persona_name = self.user_current_persona[user_key]
        current_persona_content = self.get_persona_by_name(current_persona_name)

        # 如果找不到角色卡，使用默认的system_prompt
        system_content = current_persona_content or self.system_prompt

        messages = [{"role": "system", "content": system_content}]
        messages.extend(list(self.user_contexts[user_key]))
        return messages

    def _sync_chat_with_ai(self, messages: List[dict], user_key: str) -> Optional[str]:
        """同步版本的AI聊天函数，支持完整的消息历史"""
        import requests
        import json

        # 获取当前用户选中的API密钥
        current_api_key_name = self.user_current_api_key[user_key]
        current_api_key = self.get_api_key_by_name(current_api_key_name)

        # 如果找不到API密钥，使用默认的api_key
        api_key = current_api_key or self.api_key

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        request_body = {
            "model": self.model,
            "messages": messages,
            "stream": True,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "top_p": self.top_p,
            "repetition_penalty": self.repetition_penalty
        }

        all_content_parts = []

        try:
            with requests.post(self.api_url, headers=headers, json=request_body, stream=True) as response:
                response.raise_for_status()

                for line_bytes in response.iter_lines():
                    if line_bytes:
                        decoded_line = line_bytes.decode('utf-8')

                        if decoded_line.startswith('data: '):
                            json_data_str = decoded_line[len('data: '):].strip()

                            if not json_data_str:
                                continue

                            if json_data_str == "[DONE]":
                                break

                            try:
                                json_data = json.loads(json_data_str)

                                if json_data.get("completed"):
                                    break

                                choices = json_data.get("choices")
                                if choices and len(choices) > 0:
                                    delta = choices[0].get("delta")
                                    if delta and delta.get("content"):
                                        content_piece = delta["content"]
                                        all_content_parts.append(content_piece)

                            except json.JSONDecodeError:
                                if json_data_str.strip():
                                    logger.warning(f"DZMM插件: 解析JSON时出错: '{json_data_str}'")

            if all_content_parts:
                return "".join(all_content_parts)
            else:
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"DZMM插件: 请求错误: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"DZMM插件: 发生未知错误: {str(e)}")
            return None

    async def chat_with_ai(self, messages: List[dict], user_key: str) -> Optional[str]:
        """调用AI接口进行聊天"""
        # 检查是否有可用的API密钥
        current_api_key_name = self.user_current_api_key[user_key]
        current_api_key = self.get_api_key_by_name(current_api_key_name)
        api_key = current_api_key or self.api_key

        if not api_key:
            return "错误：未配置API密钥，请联系管理员配置插件"

        try:
            # 在线程池中运行同步函数
            loop = asyncio.get_event_loop()
            with concurrent.futures.ThreadPoolExecutor() as executor:
                result = await loop.run_in_executor(
                    executor,
                    lambda: self._sync_chat_with_ai(messages, user_key)
                )

            return result if result else "抱歉，没有收到AI的回复"

        except Exception as e:
            logger.error(f"DZMM插件: 调用AI接口时发生错误: {str(e)}")
            return f"调用AI接口时发生错误: {str(e)}"

    @command("dzmm")
    async def dzmm_chat(self, event: AstrMessageEvent, content: str = None):
        """AI聊天命令"""
        if not content or not content.strip():
            yield event.plain_result(
                "使用方法：\n"
                "/dzmm [内容] - 与AI聊天\n"
                "/dzmm clear - 清除聊天上下文\n"
                "/dzmm help - 显示帮助信息\n"
                "/dzmm persona [名称] - 切换角色卡\n"
                "/dzmm key [名称] - 切换API密钥\n"
                "/dzmm personals - 查看角色卡列表\n"
                "/dzmm keyls - 查看API密钥列表\n"
                "/dzmm status - 查看当前状态"
            )
            return

        content = content.strip()
        user_key = self.get_user_key(event)

        # 处理特殊命令
        if content.lower() == "help":
            yield event.plain_result(
                "DZMM AI聊天插件帮助：\n"
                "• /dzmm [内容] - 与AI聊天，支持上下文对话\n"
                "• /dzmm clear - 清除当前用户的聊天上下文\n"
                "• /dzmm persona [名称] - 切换角色卡\n"
                "• /dzmm key [名称] - 切换API密钥\n"
                "• /dzmm personals - 查看所有角色卡\n"
                "• /dzmm keyls - 查看所有API密钥\n"
                "• /dzmm status - 查看当前选中的角色卡和密钥\n"
                "• /dzmm help - 显示此帮助信息\n\n"
                f"当前配置：\n"
                f"• 上下文长度：{self.context_length}条消息\n"
                f"• 模型：{self.model}\n"
                f"• 温度：{self.temperature}"
            )
            return

        if content.lower() == "clear":
            self.user_contexts[user_key].clear()
            yield event.plain_result("✅ 已清除聊天上下文")
            return

        # 处理角色卡切换命令
        if content.lower().startswith("persona "):
            persona_name = content[8:].strip()
            if not persona_name:
                yield event.plain_result("❌ 请指定角色卡名称")
                return

            if self.get_persona_by_name(persona_name):
                self.user_current_persona[user_key] = persona_name
                yield event.plain_result(f"✅ 已切换到角色卡：{persona_name}")
                return
            else:
                available_personas = ", ".join(self.get_persona_names())
                yield event.plain_result(f"❌ 角色卡 '{persona_name}' 不存在\n可用角色卡：{available_personas}")
                return

        # 处理API密钥切换命令
        if content.lower().startswith("key "):
            key_name = content[4:].strip()
            if not key_name:
                yield event.plain_result("❌ 请指定API密钥名称")
                return

            if self.get_api_key_by_name(key_name):
                self.user_current_api_key[user_key] = key_name
                yield event.plain_result(f"✅ 已切换到API密钥：{key_name}")
                return
            else:
                available_keys = ", ".join(self.get_api_key_names())
                yield event.plain_result(f"❌ API密钥 '{key_name}' 不存在\n可用密钥：{available_keys}")
                return

        # 处理查看角色卡列表命令
        if content.lower() == "personals":
            persona_names = self.get_persona_names()
            if persona_names:
                yield event.plain_result(f"📋 可用角色卡：\n" + "\n".join([f"• {name}" for name in persona_names]))
            else:
                yield event.plain_result("❌ 没有配置角色卡")
            return

        # 处理查看API密钥列表命令
        if content.lower() == "keyls":
            key_names = self.get_api_key_names()
            if key_names:
                yield event.plain_result(f"🔑 可用API密钥：\n" + "\n".join([f"• {name}" for name in key_names]))
            else:
                yield event.plain_result("❌ 没有配置API密钥")
            return

        # 处理查看状态命令
        if content.lower() == "status":
            current_persona = self.user_current_persona[user_key]
            current_key = self.user_current_api_key[user_key]
            yield event.plain_result(
                f"📊 当前状态：\n"
                f"• 角色卡：{current_persona}\n"
                f"• API密钥：{current_key}"
            )
            return

        # 添加用户消息到上下文
        self.add_to_context(user_key, "user", content)

        # 获取完整的消息列表
        messages = self.get_context_messages(user_key)

        # 调用AI接口
        try:
            ai_response = await self.chat_with_ai(messages, user_key)

            if ai_response:
                # 添加AI回复到上下文
                self.add_to_context(user_key, "assistant", ai_response)
                yield event.plain_result(ai_response)
            else:
                yield event.plain_result("抱歉，AI暂时无法回复")

        except Exception as e:
            logger.error(f"DZMM插件: 处理聊天时发生错误: {str(e)}")
            yield event.plain_result(f"处理聊天时发生错误: {str(e)}")
