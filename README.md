<div align="center">

![:name](https://count.getloli.com/@astrbot_plugin_dzmm?name=astrbot_plugin_dzmm&theme=nixietube-1&padding=7&offset=0&align=top&scale=1&pixelated=1&darkmode=auto)

</div>


# astrbot_plugin_dzmm

DZMM聊天插件，支持上下文对话和自定义配置。默认使用dzmm的免费试用模型，能每天50条消息。

## 功能特点

- 🤖 支持与AI进行自然对话
- 💬 维持聊天上下文，支持连续对话
- 👥 用户隔离，每个用户都有独立的聊天上下文
- ⚙️ 丰富的配置选项，支持自定义系统提示词
- 🔧 支持多种AI模型和参数调节

## 使用方法

### 基本命令

- `/dzmm [内容]` - 与AI聊天
- `/dzmm help` - 显示帮助信息
- `/dzmm clear` - 清除当前用户的聊天上下文

### 使用示例

```
/dzmm 你好，请介绍一下自己
/dzmm 刚才我们聊了什么？
/dzmm clear
```

## 配置说明

在astrbot的插件配置中，你可以设置以下参数：

### 必需配置

- **api_key**: OpenAI API密钥（必须配置，请自行在DZMM官网注册并获取，获取界面在“充值”-“API”中）

### 可选配置

- **system_prompt**: 系统提示词，默认为"你是一个有帮助的AI助手。"
- **context_length**: 上下文消息数量，默认为10条
- **api_url**: API接口地址，默认为gpt4novel接口
- **model**: 使用的模型，默认为"nalang-turbo-v23"
- **temperature**: 温度参数（0-1），控制回复的随机性，默认0.7
- **max_tokens**: 最大token数，默认800
- **top_p**: Top-p参数（0-1），默认0.35
- **repetition_penalty**: 重复惩罚系数，默认1.05

## 特性说明

### 用户隔离

插件会为每个用户维护独立的聊天上下文，用户标识由以下信息组成：
- 平台名称
- 群组ID（私聊时为"private"）
- 用户ID

### 上下文管理

- 自动维护指定数量的历史消息
- 超出限制时自动清理最旧的消息
- 支持手动清除上下文

### 错误处理

- API密钥未配置时会给出明确提示
- 网络错误时会自动重试
- 详细的错误日志记录

## 安装说明

1. 将插件文件放置到astrbot的插件目录
2. 在astrbot配置中启用插件
3. 配置API密钥和其他参数
4. 重启astrbot

## 注意事项

- 请确保API密钥有效且有足够的额度
- 上下文长度设置过大可能会增加API调用成本
- 建议根据实际需求调整温度和其他参数
- 插件已包含command装饰器的兼容性处理，支持不同版本的astrbot

## 故障排除

### 常见问题

#### 1. 插件加载失败
如果遇到"cannot import name 'command'"错误：
- 插件已包含自动兼容处理
- 确保使用最新版本的插件代码
- 检查astrbot版本是否支持

#### 2. 命令无响应
- 确保使用正确的命令格式：`/dzmm [内容]`
- 检查API密钥配置是否正确
- 查看astrbot日志获取详细错误信息

## 版本历史

- v1.0.0: 初始版本，支持基本聊天和上下文管理

## 许可证

MIT License
