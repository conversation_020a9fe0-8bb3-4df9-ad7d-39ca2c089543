# DZMM插件部署检查清单

## 📋 部署前检查

### ✅ 文件完整性
- [ ] `main.py` - 主插件文件
- [ ] `metadata.yaml` - 插件元数据
- [ ] `_conf_schema.json` - 配置模式
- [ ] `README.md` - 使用说明
- [ ] `INSTALL.md` - 安装指南

### ✅ 配置准备
- [ ] 获取有效的API密钥
- [ ] 确定系统提示词内容
- [ ] 设置合适的上下文长度
- [ ] 选择合适的模型参数

## 🚀 部署步骤

### 1. 文件部署
```bash
# 将插件文件夹复制到astrbot插件目录
cp -r astrbot_plugin_dzmm /path/to/astrbot/data/plugins/
```

### 2. 配置插件
在astrbot Web管理界面中：
1. 进入"插件管理"
2. 找到"astrbot_plugin_dzmm"
3. 点击"配置"
4. 填写必要配置项：
   - **api_key**: 你的API密钥（必需）
   - **system_prompt**: 系统提示词（可选）
   - **context_length**: 上下文长度（默认10）

### 3. 启用插件
1. 在插件列表中启用插件
2. 重启astrbot服务

### 4. 测试功能
```
/dzmm help
/dzmm 你好，请介绍一下自己
/dzmm clear
```

## 🔧 故障排除

### 常见错误及解决方案

#### 1. ImportError: cannot import name 'command'
**原因**: astrbot版本兼容性问题
**解决**: 插件已包含自动兼容处理，确保使用最新版本

#### 2. 插件加载失败
**检查项**:
- [ ] 文件权限是否正确
- [ ] 文件路径是否正确
- [ ] Python语法是否有错误

#### 3. API调用失败
**检查项**:
- [ ] API密钥是否有效
- [ ] 网络连接是否正常
- [ ] API服务是否可用

#### 4. 命令无响应
**检查项**:
- [ ] 命令格式是否正确（`/dzmm [内容]`）
- [ ] 插件是否已启用
- [ ] 查看astrbot日志

## 📊 性能优化建议

### 内存使用
- 上下文长度建议设置为10-20条
- 定期使用`/dzmm clear`清理上下文

### API成本控制
- 合理设置max_tokens参数
- 根据需求调整上下文长度
- 选择成本效益高的模型

### 响应速度
- 选择响应快的API服务
- 优化网络连接
- 适当降低max_tokens

## 🔍 监控和维护

### 日志监控
关注以下日志关键词：
- `DZMM插件`
- `API请求错误`
- `解析JSON时出错`
- `调用AI接口时发生错误`

### 定期检查
- [ ] API密钥余额
- [ ] 插件运行状态
- [ ] 用户反馈
- [ ] 性能指标

### 更新维护
- [ ] 定期检查插件更新
- [ ] 备份配置设置
- [ ] 测试新功能

## 📞 技术支持

如果遇到问题：
1. 查看本检查清单
2. 检查astrbot和插件日志
3. 在项目仓库提交Issue
4. 提供详细的错误信息和环境信息

## ✅ 部署完成确认

部署完成后，确认以下功能正常：
- [ ] 插件成功加载
- [ ] 基本聊天功能正常
- [ ] 上下文功能正常
- [ ] 用户隔离功能正常
- [ ] 特殊命令（help、clear）正常
- [ ] 错误处理正常

---

**部署日期**: ___________
**部署人员**: ___________
**astrbot版本**: ___________
**插件版本**: v1.0.0
